<?php

// Show all errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

defined('YII_DEBUG') or define('YII_DEBUG', true);
defined('YII_ENV') or define('YII_ENV', 'dev');

try {
    require(__DIR__ . '/../../vendor/autoload.php');
    echo "Autoload loaded successfully<br>";
    
    require(__DIR__ . '/../../vendor/yiisoft/yii2/Yii.php');
    echo "Yii loaded successfully<br>";
    
    // Minimal config
    $config = [
        'id' => 'test-app',
        'basePath' => dirname(__DIR__),
        'components' => [
            'request' => [
                'cookieValidationKey' => 'test-key',
            ],
        ],
    ];
    
    $app = new yii\web\Application($config);
    echo "Yii application created successfully<br>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "Trace: <pre>" . $e->getTraceAsString() . "</pre>";
}
?>
