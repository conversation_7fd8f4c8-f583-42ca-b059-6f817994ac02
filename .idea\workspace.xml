<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="BranchesTreeState">
    <expand>
      <path>
        <item name="ROOT" type="e8cecc67:BranchNodeDescriptor" />
        <item name="LOCAL_ROOT" type="e8cecc67:BranchNodeDescriptor" />
      </path>
    </expand>
    <select />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="6541e5d5-ab13-4e35-80ae-2ad02cb2eb20" name="Default Changelist" comment="">
      <change afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/frontend/web/.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.htaccess" beforeDir="false" afterPath="$PROJECT_DIR$/.htaccess" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/console/config/main.php" beforeDir="false" afterPath="$PROJECT_DIR$/console/config/main.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/config/main.php" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/config/main.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/web/.htaccess" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/web/.htaccess" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings" doNotAsk="true" synchronizationState="SYNCHRONIZE">
    <pharConfigPath>$PROJECT_DIR$/composer.json</pharConfigPath>
    <execution />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitToolBoxStore">
    <option name="projectConfigVersion" value="2" />
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="main" />
                    <option name="lastUsedInstant" value="1755024542" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1755024541" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="PhpWorkspaceProjectConfiguration">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-debug" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-swiftmailer" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-bootstrap" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-gii" />
      <path value="$PROJECT_DIR$/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/vendor/kartik-v/yii2-widget-depdrop" />
      <path value="$PROJECT_DIR$/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/vendor/kartik-v/dependent-dropdown" />
      <path value="$PROJECT_DIR$/vendor/kartik-v/bootstrap-fileinput" />
      <path value="$PROJECT_DIR$/vendor/kartik-v/yii2-widget-select2" />
      <path value="$PROJECT_DIR$/vendor/kartik-v/yii2-krajee-base" />
      <path value="$PROJECT_DIR$/vendor/kartik-v/yii2-widget-fileinput" />
      <path value="$PROJECT_DIR$/vendor/kartik-v/yii2-widget-datepicker" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/vendor/ezyang/htmlpurifier" />
      <path value="$PROJECT_DIR$/vendor/behat/gherkin" />
      <path value="$PROJECT_DIR$/vendor/codeception/stub" />
      <path value="$PROJECT_DIR$/vendor/codeception/phpunit-wrapper" />
      <path value="$PROJECT_DIR$/vendor/codeception/base" />
      <path value="$PROJECT_DIR$/vendor/codeception/verify" />
      <path value="$PROJECT_DIR$/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/symfony/css-selector" />
      <path value="$PROJECT_DIR$/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/vendor/phar-io/version" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/fzaninotto/faker" />
      <path value="$PROJECT_DIR$/vendor/phar-io/manifest" />
      <path value="$PROJECT_DIR$/vendor/symfony/console" />
      <path value="$PROJECT_DIR$/vendor/symfony/browser-kit" />
      <path value="$PROJECT_DIR$/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/vendor/symfony/debug" />
      <path value="$PROJECT_DIR$/vendor/symfony/yaml" />
      <path value="$PROJECT_DIR$/vendor/symfony/dom-crawler" />
      <path value="$PROJECT_DIR$/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/vendor/psr/log" />
      <path value="$PROJECT_DIR$/vendor/theseer/tokenizer" />
      <path value="$PROJECT_DIR$/vendor/sebastian/resource-operations" />
      <path value="$PROJECT_DIR$/vendor/sebastian/recursion-context" />
      <path value="$PROJECT_DIR$/vendor/sebastian/global-state" />
      <path value="$PROJECT_DIR$/vendor/sebastian/diff" />
      <path value="$PROJECT_DIR$/vendor/doctrine/instantiator" />
      <path value="$PROJECT_DIR$/vendor/sebastian/exporter" />
      <path value="$PROJECT_DIR$/vendor/swiftmailer/swiftmailer" />
      <path value="$PROJECT_DIR$/vendor/sebastian/object-enumerator" />
      <path value="$PROJECT_DIR$/vendor/sebastian/environment" />
      <path value="$PROJECT_DIR$/vendor/sebastian/comparator" />
      <path value="$PROJECT_DIR$/vendor/sebastian/object-reflector" />
      <path value="$PROJECT_DIR$/vendor/sebastian/version" />
      <path value="$PROJECT_DIR$/vendor/perminder-klair/yii2-dropzone" />
      <path value="$PROJECT_DIR$/vendor/sebastian/code-unit-reverse-lookup" />
      <path value="$PROJECT_DIR$/vendor/phpspec/php-diff" />
      <path value="$PROJECT_DIR$/vendor/phpspec/prophecy" />
      <path value="$PROJECT_DIR$/vendor/bower/typeahead.js" />
      <path value="$PROJECT_DIR$/vendor/bower/jquery" />
      <path value="$PROJECT_DIR$/vendor/bower/bootstrap" />
      <path value="$PROJECT_DIR$/vendor/bower/yii2-pjax" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-token-stream" />
      <path value="$PROJECT_DIR$/vendor/bower/inputmask" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-text-template" />
      <path value="$PROJECT_DIR$/vendor/bower/punycode" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-timer" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-file-iterator" />
      <path value="$PROJECT_DIR$/vendor/phpunit/phpunit-mock-objects" />
      <path value="$PROJECT_DIR$/vendor/phpunit/php-code-coverage" />
      <path value="$PROJECT_DIR$/vendor/cebe/markdown" />
      <path value="$PROJECT_DIR$/vendor/phpunit/phpunit" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-httpclient" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-composer" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-faker" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/bootstrap" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/inputmask" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/jquery" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/punycode" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/yii2-pjax" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/typeahead.js" />
      <path value="$PROJECT_DIR$/vendor/markbaker/matrix" />
      <path value="$PROJECT_DIR$/vendor/mpdf/mpdf" />
      <path value="$PROJECT_DIR$/vendor/phpoffice/phpspreadsheet" />
      <path value="$PROJECT_DIR$/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/vendor/setasign/fpdi" />
      <path value="$PROJECT_DIR$/vendor/markbaker/complex" />
      <path value="$PROJECT_DIR$/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/vendor/kartik-v/yii2-sortable" />
      <path value="$PROJECT_DIR$/vendor/kartik-v/yii2-mpdf" />
      <path value="$PROJECT_DIR$/vendor/kartik-v/yii2-widget-activeform" />
      <path value="$PROJECT_DIR$/vendor/kartik-v/yii2-bootstrap4-dropdown" />
      <path value="$PROJECT_DIR$/vendor/kartik-v/yii2-export" />
      <path value="$PROJECT_DIR$/vendor/kartik-v/yii2-dynagrid" />
      <path value="$PROJECT_DIR$/vendor/kartik-v/yii2-grid" />
      <path value="$PROJECT_DIR$/vendor/kartik-v/yii2-dialog" />
      <path value="$PROJECT_DIR$/vendor/wbraganca/yii2-dynamicform" />
      <path value="$PROJECT_DIR$/vendor/select2/select2" />
    </include_path>
  </component>
  <component name="ProjectId" id="1eOOYu6WyxKZ5H7PauYYxlsXsyE" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="6541e5d5-ab13-4e35-80ae-2ad02cb2eb20" name="Default Changelist" comment="" />
      <created>1594189224135</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1594189224135</updated>
      <workItem from="1594189226732" duration="156000" />
      <workItem from="1594192096042" duration="221000" />
      <workItem from="1594192341563" duration="6066000" />
      <workItem from="1594275081078" duration="5829000" />
      <workItem from="1594390285740" duration="2903000" />
      <workItem from="1594402332954" duration="4414000" />
      <workItem from="1594435756160" duration="21766000" />
      <workItem from="1594548242438" duration="46522000" />
      <workItem from="1597299900089" duration="37000" />
      <workItem from="1597299938667" duration="47000" />
      <workItem from="1598244262120" duration="2068000" />
      <workItem from="1598426843753" duration="18000" />
      <workItem from="1599106855828" duration="5528000" />
      <workItem from="1599224140414" duration="12666000" />
      <workItem from="1619561176090" duration="392000" />
      <workItem from="1619583078348" duration="45000" />
      <workItem from="1619605384497" duration="8967000" />
      <workItem from="1755021439465" duration="3671000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="2" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="WindowStateProjectService">
    <state x="463" y="118" key="FileChooserDialogImpl" timestamp="1619583121777">
      <screen x="0" y="0" width="1366" height="728" />
    </state>
    <state x="463" y="118" key="FileChooserDialogImpl/0.0.1366.728@0.0.1366.728" timestamp="1619583121777" />
  </component>
</project>