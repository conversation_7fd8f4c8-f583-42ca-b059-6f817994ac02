Options +FollowSymLinks
IndexIgnore */*
RewriteEngine on

# HTTPS redirect
RewriteCond %{HTTPS} off
RewriteRule (.*) https://%{HTTP_HOST}%{REQUEST_URI} [R=301,L]

# Redirect everything to frontend/web except if it's already there
RewriteCond %{REQUEST_URI} !^/frontend/web/
RewriteRule ^(.*)$ /frontend/web/$1 [L]

# Handle frontend requests
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^frontend/web/(.*)$ /frontend/web/index.php [L]

# php -- <PERSON><PERSON><PERSON> cPanel-generated handler, do not edit
# This domain inherits the “PHP” package.
# php -- END cPanel-generated handler, do not edit

