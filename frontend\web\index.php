<?php

// Show all errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

defined('YII_DEBUG') or define('YII_DEBUG', true);
defined('YII_ENV') or define('YII_ENV', 'dev');

try {
    echo "Loading autoload...<br>";
    require(__DIR__ . '/../../vendor/autoload.php');
    echo "Autoload loaded successfully<br>";

    echo "Loading Yii...<br>";
    require(__DIR__ . '/../../vendor/yiisoft/yii2/Yii.php');
    echo "Yii loaded successfully<br>";

    echo "Loading common bootstrap...<br>";
    require(__DIR__ . '/../../common/config/bootstrap.php');
    echo "Common bootstrap loaded successfully<br>";

    echo "Loading frontend bootstrap...<br>";
    require(__DIR__ . '/../config/bootstrap.php');
    echo "Frontend bootstrap loaded successfully<br>";

    echo "Loading configuration files...<br>";
    $config = yii\helpers\ArrayHelper::merge(
        require(__DIR__ . '/../../common/config/main.php'),
        require(__DIR__ . '/../../common/config/main-local.php'),
        require(__DIR__ . '/../config/main.php'),
        require(__DIR__ . '/../config/main-local.php')
    );
    echo "Configuration loaded successfully<br>";

    echo "Creating Yii application...<br>";
    $app = new yii\web\Application($config);
    echo "Application created successfully<br>";

    echo "Running application...<br>";
    $app->run();

} catch (Exception $e) {
    echo "<h2>Error occurred:</h2>";
    echo "Message: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "<h3>Stack trace:</h3>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
} catch (Error $e) {
    echo "<h2>Fatal Error occurred:</h2>";
    echo "Message: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "<h3>Stack trace:</h3>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

